import { Client, Account, Databases, Storage, Query, ID } from 'appwrite';

// Initialize client, account, database, and storage lazily
let client = null;
let account = null;
let databases = null;
let storage = null;

const initializeAppwrite = () => {
  if (!client) {
    const config = useRuntimeConfig();

    client = new Client();
    client
      .setEndpoint(config.public.appwriteEndpoint)
      .setProject(config.public.appwriteProjectId);

    // Debug: Log the configuration
    console.log('Appwrite Config:', {
      endpoint: config.public.appwriteEndpoint,
      projectId: config.public.appwriteProjectId
    });

    account = new Account(client);
    databases = new Databases(client);
    storage = new Storage(client);
  }
  return { client, account, databases, storage };
};

// Authentication methods
export const appwriteService = {
  // Create a new session (login)
  async login(email, password) {
    try {
      const { account } = initializeAppwrite();

      // First, try to delete any existing session
      try {
        await account.deleteSession('current');
        console.log('Existing session deleted');
      } catch (error) {
        console.log('No existing session to delete:', error.message);
      }

      // Now create a new session
      const session = await account.createEmailPasswordSession(email, password);
      console.log('Login successful:', session);
      return session;
    } catch (error) {
      console.error('Login error details:', {
        message: error.message,
        code: error.code,
        type: error.type,
        version: error.version
      });

      // Provide more helpful error messages
      if (error.code === 401 && error.type === 'general_unauthorized_scope') {
        throw new Error('Authentication failed: Please check your Appwrite project permissions. Guest users may not have account access enabled.');
      } else if (error.code === 401) {
        throw new Error('Invalid email or password. Please check your credentials.');
      } else {
        throw new Error(error.message || 'Login failed. Please try again.');
      }
    }
  },

  // Create a new account
  async register(email, password, name) {
    const { account } = initializeAppwrite();
    return await account.create('unique()', email, password, name);
  },

  // Get the current user
  async getCurrentUser() {
    try {
      const { account } = initializeAppwrite();
      return await account.get();
    } catch (error) {
      return null;
    }
  },

  // Check if user is logged in
  async isLoggedIn() {
    try {
      const { account } = initializeAppwrite();
      await account.get();
      return true;
    } catch (error) {
      return false;
    }
  },

  // Logout
  async logout() {
    const { account } = initializeAppwrite();
    return await account.deleteSession('current');
  },

  // Profile methods
  async getUserProfile(userId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();
      const response = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteProfileCollectionId,
        [Query.equal('UserID', userId)]
      );

      return response.documents.length > 0 ? response.documents[0] : null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  },

  async createUserProfile(profileData) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();
      return await databases.createDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteProfileCollectionId,
        'unique()', // Document ID
        profileData
      );
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  },

  async updateUserProfile(documentId, profileData) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();
      return await databases.updateDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteProfileCollectionId,
        documentId,
        profileData
      );
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  // Logging methods
  async createLog(userId, logAction) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      const logData = {
        UserID: userId,
        LogAction: logAction
      };

      return await databases.createDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteLogCollectionId,
        'unique()', // Document ID
        logData
      );
    } catch (error) {
      console.error('Error creating log entry:', error);
      // Don't throw error for logging - we don't want logging failures to break the app
    }
  },

  async createLogWithIP(userId, logAction, ipAddress) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      const logData = {
        UserID: userId,
        LogAction: logAction,
        IPAddress: ipAddress
      };

      console.log('Creating log with IP:', logData);

      return await databases.createDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteLogCollectionId,
        'unique()', // Document ID
        logData
      );
    } catch (error) {
      console.error('Error creating log entry with IP:', error);
      // Don't throw error for logging - we don't want logging failures to break the app
    }
  },

  async getUserLogs(userId, limit = 10) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      const response = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteLogCollectionId,
        [
          Query.equal('UserID', userId),
          Query.orderDesc('$createdAt'),
          Query.limit(limit)
        ]
      );

      return response.documents;
    } catch (error) {
      console.error('Error fetching user logs:', error);
      return [];
    }
  },

  async clearUserLogs(userId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      // First, get all user logs
      const response = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteLogCollectionId,
        [Query.equal('UserID', userId)]
      );

      // Delete each log document
      const deletePromises = response.documents.map(doc =>
        databases.deleteDocument(
          config.public.appwriteDatabaseId,
          config.public.appwriteLogCollectionId,
          doc.$id
        )
      );

      await Promise.all(deletePromises);
      return true;
    } catch (error) {
      console.error('Error clearing user logs:', error);
      throw error;
    }
  },

  // Consent methods
  async createConsent(userId, email, name, procedure, status = 'Started', documentIdSent = null) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      const consentData = {
        UserID: userId,
        Email: email,
        Name: name,        // Add patient name
        Procedure: procedure,
        Status: status
      };

      // Add DocumentIDSent if provided
      if (documentIdSent) {
        consentData.DocumentIDSent = documentIdSent;
      }

      console.log('Creating consent record:', consentData);

      // Save to Appwrite database
      const savedRecord = await databases.createDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteConsentCollectionId,
        'unique()', // Document ID
        consentData
      );

      console.log('Consent saved to Appwrite:', savedRecord);

      // Send to Go backend via secure server API
      try {
        const backendResponse = await $fetch('/api/consent/start', {
          method: 'POST',
          body: {
            consentRecord: savedRecord
          }
        });

        console.log('Go backend notified successfully:', backendResponse);
      } catch (backendError) {
        console.error('Failed to notify Go backend:', backendError);
        // Don't throw - we don't want to fail the consent creation if backend is down
        // The consent is still saved in Appwrite
      }

      return savedRecord;
    } catch (error) {
      console.error('Error creating consent record:', error);
      throw error;
    }
  },

  async getUserConsents(userId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      const response = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteConsentCollectionId,
        [
          Query.equal('UserID', userId),
          Query.orderDesc('$createdAt')
        ]
      );

      return response.documents;
    } catch (error) {
      console.error('Error fetching user consents:', error);
      return [];
    }
  },

  async getConsentById(consentId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('Fetching consent by ID:', consentId);

      const document = await databases.getDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteConsentCollectionId,
        consentId
      );

      console.log('Found consent document:', document);
      return document;
    } catch (error) {
      console.error('Error fetching consent by ID:', error);
      if (error.code === 404) {
        return null; // Document not found
      }
      throw error;
    }
  },

  // Reports methods
  async getUserReports(userId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      const response = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteReportsCollectionId,
        [
          Query.equal('UserID', userId),
          Query.orderDesc('$createdAt')
        ]
      );

      return response.documents;
    } catch (error) {
      console.error('Error fetching user reports:', error);
      return [];
    }
  },

  async getReportsAnalytics(userId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('=== REPORTS ANALYTICS CROSS-REFERENCE ===');
      console.log('Getting consents for user:', userId);

      // First, get all consents for this user
      const consentsResponse = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteConsentCollectionId,
        [
          Query.equal('UserID', userId),
          Query.orderDesc('$createdAt')
        ]
      );

      console.log('Found consents:', consentsResponse.documents.length);
      console.log('Consent IDs:', consentsResponse.documents.map(c => c.$id));

      // Get all reports (no UserID filter since Reports collection uses QRCode field)
      const allReportsResponse = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteReportsCollectionId,
        [
          Query.orderDesc('$createdAt')
        ]
      );

      console.log('Total reports in collection:', allReportsResponse.documents.length);

      // Cross-reference: Filter reports where QRCode matches any consent $id
      const consentIds = consentsResponse.documents.map(consent => consent.$id);
      const userReports = allReportsResponse.documents.filter(report => {
        const isMatch = consentIds.includes(report.QRCode);
        if (isMatch) {
          console.log(`Report ${report.$id} matches consent ${report.QRCode}`);
        }
        return isMatch;
      });

      console.log('Filtered reports for user:', userReports.length);
      console.log('=== END REPORTS ANALYTICS ===');

      return {
        reports: userReports,
        consents: consentsResponse.documents
      };
    } catch (error) {
      console.error('Error fetching reports analytics:', error);
      return { reports: [], consents: [] };
    }
  },

  // Patients methods
  async createPatient(name, email, hospitalNumber, maleFemale, dob, loginUserId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('=== CREATE PATIENT DEBUG ===');
      console.log('Received parameters:');
      console.log('- name:', name);
      console.log('- email:', email);
      console.log('- hospitalNumber:', hospitalNumber);
      console.log('- maleFemale:', maleFemale);
      console.log('- dob:', dob);
      console.log('- loginUserId:', loginUserId);
      console.log('- loginUserId type:', typeof loginUserId);

      const patientData = {
        Name: name,
        Email: email,
        HospitalNumber: hospitalNumber,
        MaleFemale: maleFemale,
        DOB: dob,
        LoginUserID: loginUserId  // Hidden field - tracks who added the patient
      };

      console.log('Final patient data to save:', patientData);

      return await databases.createDocument(
        config.public.appwriteDatabaseId,
        config.public.appwritePatientsCollectionId,
        'unique()', // Document ID
        patientData
      );
    } catch (error) {
      console.error('Error creating patient record:', error);
      throw error;
    }
  },

  async getPatients() {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('=== LOADING PATIENTS ===');
      console.log('Database ID:', config.public.appwriteDatabaseId);
      console.log('Patients Collection ID:', config.public.appwritePatientsCollectionId);

      const response = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwritePatientsCollectionId,
        [
          Query.orderDesc('$createdAt')
        ]
      );

      console.log('Patients response:', response);
      console.log('Number of patients found:', response.documents.length);
      console.log('Patient documents:', response.documents);

      return response.documents;
    } catch (error) {
      console.error('Error fetching patients:', error);
      console.error('Error details:', error.message);
      return [];
    }
  },

  async getPatientById(patientId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('Getting patient with ID:', patientId);

      const patient = await databases.getDocument(
        config.public.appwriteDatabaseId,
        config.public.appwritePatientsCollectionId,
        patientId
      );

      console.log('Patient retrieved successfully:', patient);
      return patient;
    } catch (error) {
      console.error('Error getting patient by ID:', error);
      throw error;
    }
  },

  async deletePatient(patientId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('Deleting patient with ID:', patientId);

      await databases.deleteDocument(
        config.public.appwriteDatabaseId,
        config.public.appwritePatientsCollectionId,
        patientId
      );

      console.log('Patient deleted from database successfully');
      return true;
    } catch (error) {
      console.error('Error deleting patient:', error);
      throw error;
    }
  },

  // Document Storage methods
  async uploadDocument(file, patientId) {
    try {
      const { storage } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('=== UPLOADING DOCUMENT ===');
      console.log('File:', file.name, 'Size:', file.size, 'Type:', file.type);
      console.log('Patient ID:', patientId);
      console.log('Storage Bucket ID:', config.public.appwriteDocumentStorageBucketId);
      console.log('Storage service initialized:', !!storage);

      // Validate storage bucket ID
      if (!config.public.appwriteDocumentStorageBucketId) {
        throw new Error('Storage bucket ID not configured');
      }

      // Create a unique file ID with patient reference (use ID.unique() for better compatibility)
      const fileId = ID.unique();
      console.log('Generated File ID:', fileId);

      // Upload file to Appwrite Storage
      const uploadedFile = await storage.createFile(
        config.public.appwriteDocumentStorageBucketId,
        fileId,
        file
      );

      console.log('Document uploaded successfully:', uploadedFile);
      return uploadedFile;
    } catch (error) {
      console.error('Error uploading document:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        type: error.type,
        response: error.response
      });
      throw error;
    }
  },

  async getDocumentUrl(fileId) {
    try {
      const { storage } = initializeAppwrite();
      const config = useRuntimeConfig();

      // Get file preview/download URL
      const fileUrl = storage.getFileView(
        config.public.appwriteDocumentStorageBucketId,
        fileId
      );

      return fileUrl;
    } catch (error) {
      console.error('Error getting document URL:', error);
      throw error;
    }
  },

  async deleteDocument(fileId) {
    try {
      const { storage } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('Deleting document with ID:', fileId);

      await storage.deleteFile(
        config.public.appwriteDocumentStorageBucketId,
        fileId
      );

      console.log('Document deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  },

  // DocumentsUpload Collection methods
  async createDocumentUploadRecord(userId, patientUserId, documentId, procedureType = null) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('=== CREATING DOCUMENT UPLOAD RECORD ===');
      console.log('UserID:', userId);
      console.log('PatientUserID:', patientUserId);
      console.log('DocumentID:', documentId);
      console.log('ProcedureType:', procedureType);

      // Always create a new record for each document upload
      const documentUploadData = {
        UserID: userId,
        PatientUserID: patientUserId,
        Documents: documentId // Single document ID (string, not array)
      };

      // Add ProcedureType if provided
      if (procedureType) {
        documentUploadData.ProcedureType = procedureType;
      }

      console.log('Creating new document upload record:', documentUploadData);

      const newRecord = await databases.createDocument(
        config.public.appwriteDatabaseId,
        config.public.appwriteDocumentsUploadCollectionId,
        ID.unique(),
        documentUploadData
      );

      console.log('Document upload record created:', newRecord);
      return newRecord;
    } catch (error) {
      console.error('Error creating document upload record:', error);
      throw error;
    }
  },

  async removeDocumentFromUploadRecord(userId, patientUserId, documentId) {
    try {
      const { databases } = initializeAppwrite();
      const config = useRuntimeConfig();

      console.log('=== REMOVING DOCUMENT FROM UPLOAD RECORD ===');
      console.log('UserID:', userId);
      console.log('PatientUserID:', patientUserId);
      console.log('DocumentID to remove:', documentId);

      // Find the specific record with this document ID
      const existingRecords = await databases.listDocuments(
        config.public.appwriteDatabaseId,
        config.public.appwriteDocumentsUploadCollectionId,
        [
          Query.equal('UserID', userId),
          Query.equal('PatientUserID', patientUserId),
          Query.equal('Documents', documentId) // Find record with this specific document
        ]
      );

      if (existingRecords.documents.length > 0) {
        const recordToDelete = existingRecords.documents[0];

        console.log('Deleting entire document upload record');
        await databases.deleteDocument(
          config.public.appwriteDatabaseId,
          config.public.appwriteDocumentsUploadCollectionId,
          recordToDelete.$id
        );

        console.log('Document upload record deleted successfully');
        return true;
      } else {
        console.log('No record found with this document ID');
        return null;
      }
    } catch (error) {
      console.error('Error removing document upload record:', error);
      throw error;
    }
  }
};