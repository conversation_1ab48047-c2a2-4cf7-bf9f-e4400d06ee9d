<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <!-- Initial Page Loading Overlay -->
    <div v-if="patientsLoading && patients.length === 0" class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-20 w-20 border-b-4 border-blue-600 mx-auto mb-6"></div>
        <h2 class="text-2xl font-bold text-gray-800 mb-2">Loading Patient Management</h2>
        <p class="text-lg text-gray-600">Fetching patient data...</p>
        <div class="mt-4">
          <div class="bg-gray-200 rounded-full h-2 w-64 mx-auto">
            <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 60%"></div>
          </div>
        </div>
      </div>
    </div>



    <!-- <PERSON> Header -->
    <div class="mb-8">
      <div class="bg-gray-100 rounded-2xl p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Patient Management</h1>
        <p class="text-gray-700">Add and manage patient records</p>
      </div>
    </div>

    <!-- Add Patient Form -->
    <div class="bg-white rounded-lg shadow-md mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Add New Patient</h2>
      </div>
      <div class="p-6">
        <form @submit.prevent="handleAddPatient" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="patientName" class="block text-sm font-medium text-gray-700">Patient Name</label>
              <input
                id="patientName"
                v-model="newPatient.Name"
                type="text"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter patient name"
              />
            </div>
            <div>
              <label for="patientEmail" class="block text-sm font-medium text-gray-700">Patient Email</label>
              <input
                id="patientEmail"
                v-model="newPatient.Email"
                type="email"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label for="hospitalNumber" class="block text-sm font-medium text-gray-700">Hospital Number</label>
              <input
                id="hospitalNumber"
                v-model="newPatient.HospitalNumber"
                type="text"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter hospital number"
              />
            </div>
            <div>
              <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
              <select
                id="gender"
                v-model="newPatient.MaleFemale"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
              </select>
            </div>
            <div>
              <label for="dob" class="block text-sm font-medium text-gray-700">Date of Birth</label>
              <input
                id="dob"
                v-model="newPatient.DOB"
                type="date"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <!-- Document URL - Commented out for now -->
          <!--
          <div>
            <label for="docUrl" class="block text-sm font-medium text-gray-700">Document URL</label>
            <input
              id="docUrl"
              v-model="newPatient.DocURL"
              type="url"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://example.com/document"
            />
          </div>
          -->

          <!-- Error Message -->
          <div v-if="error" class="text-red-600 text-sm">
            {{ error }}
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="isLoading"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed hover:opacity-90"
              style="background-color: #65ADEE; --tw-ring-color: #65ADEE;"
            >
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLoading ? 'Adding...' : 'Add Patient' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Patients List -->
    <div class="bg-white rounded-lg shadow-md">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Patient List</h2>
      </div>
      <div class="p-6">
        <!-- Loading state -->
        <div v-if="patientsLoading" class="text-center py-16">
          <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto mb-4"></div>
          <p class="text-lg text-gray-600 font-medium">Loading patient list...</p>
          <p class="text-sm text-gray-500 mt-2">Please wait while we fetch the patient data</p>
        </div>

        <!-- Patients table -->
        <div v-else-if="patients.length > 0" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hospital</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DoB</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Added</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="patient in patients" :key="patient.$id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ patient.Name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ patient.Email }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ patient.HospitalNumber || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ patient.MaleFemale || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDateOfBirth(patient.DOB) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(patient.$createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <!-- Create Document Button -->
                  <NuxtLink :to="`/create-document/${patient.$id}`">
                    <button
                      class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors inline-flex items-center"
                    >
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                      </svg>
                      Create Document
                    </button>
                  </NuxtLink>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    @click="handleDeletePatient(patient)"
                    :disabled="isDeleting === patient.$id"
                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <svg v-if="isDeleting === patient.$id" class="animate-spin -ml-1 mr-1 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <svg v-else class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    {{ isDeleting === patient.$id ? 'Deleting...' : 'Delete' }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- No patients -->
        <div v-else class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No patients</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by adding your first patient.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuth } from '~/composables/useAuth'
import { useLogger } from '~/composables/useLogger'

// Protect this page - require authentication
definePageMeta({
  middleware: 'auth'
})

// Use auth composable
const { isLoggedIn, currentUser, checkAuth } = useAuth()
const { logAction, LOG_ACTIONS } = useLogger()

// State management
const patients = ref([])
const patientsLoading = ref(true) // Start with true for initial load
const isLoading = ref(false)
const isDeleting = ref(null) // Track which patient is being deleted
const error = ref('')

// New patient form data
const newPatient = reactive({
  Name: '',
  Email: '',
  HospitalNumber: '',
  MaleFemale: '',
  DOB: ''
  // DocURL: '' // Commented out since we're using Create Document button instead
})

// Handle create document button
const handleCreateDocument = (patient) => {
  // For now, just show an alert with patient info - you can implement document creation logic here
  //alert(`Create Document for: ${patient.Name} (${patient.Email})\nHospital Number: ${patient.HospitalNumber}`)
}

// Add new patient
const handleAddPatient = async () => {
  try {
    isLoading.value = true
    error.value = ''


    // Check if user is logged in
    if (!currentUser.value || !currentUser.value.$id) {
      throw new Error('User not logged in or user ID not available')
    }

    // Import Appwrite service
    const { appwriteService } = await import('~/services/appwrite')

    // Add patient to database (includes LoginUserID automatically)
    const patientRecord = await appwriteService.createPatient(
      newPatient.Name,
      newPatient.Email,
      newPatient.HospitalNumber,
      newPatient.MaleFemale,
      newPatient.DOB,
      currentUser.value.$id  // LoginUserID - tracks who added this patient
    )

    console.log('Patient added:', patientRecord)

    // Log the action
    await logAction(currentUser.value.$id, 'Added new patient')

    // Reset form
    newPatient.Name = ''
    newPatient.Email = ''
    newPatient.HospitalNumber = ''
    newPatient.MaleFemale = ''
    newPatient.DOB = ''
    // newPatient.DocURL = '' // Commented out since we're not using the field

    // Reload patients list
    await loadPatients()

    console.log('Patient added successfully!')

  } catch (err) {
    console.error('Error adding patient:', err)
    error.value = err.message || 'Failed to add patient. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// Delete patient
const handleDeletePatient = async (patient) => {
  try {
    // Confirm deletion
    const confirmed = confirm(`Are you sure you want to delete patient "${patient.Name}"? This action cannot be undone.`)
    if (!confirmed) return

    isDeleting.value = patient.$id

    console.log('Deleting patient:', patient.Name, 'ID:', patient.$id)

    // Import Appwrite service
    const { appwriteService } = await import('~/services/appwrite')

    // Delete patient from database
    await appwriteService.deletePatient(patient.$id)

    console.log('Patient deleted successfully')

    // Log the action
    await logAction(currentUser.value.$id, `Deleted patient: ${patient.Name}`)

    // Reload patients list
    await loadPatients()

  } catch (err) {
    console.error('Error deleting patient:', err)
    alert('Failed to delete patient. Please try again.')
  } finally {
    isDeleting.value = null
  }
}

// Load patients list
const loadPatients = async () => {
  try {
    patientsLoading.value = true

    // Import Appwrite service
    const { appwriteService } = await import('~/services/appwrite')

    // Get all patients (no user filtering for now)
    patients.value = await appwriteService.getPatients()

    console.log('Loaded patients:', patients.value.length)
  } catch (err) {
    console.error('Error loading patients:', err)
  } finally {
    patientsLoading.value = false
  }
}

// Helper functions
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateOfBirth = (dateString) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString)
  return date.toLocaleDateString('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}



// Check authentication and load data when component mounts
onMounted(async () => {
 
  await checkAuth()


  if (isLoggedIn.value && currentUser.value) {

    await logAction(currentUser.value.$id, 'Viewed patient list')
    await loadPatients()
  } else {
    console.log('User not authenticated or currentUser is null')
  }
})

// Set page title
useHead({
  title: 'Patient Management | Surgassists'
})
</script>
