<template>

    <div class="">
        

          <!-- Top Image -->
       <div class="w-full" style="background-color: #EFF9FC;">
    <div class="text-center">
      <img src="/images/TopImage.png" alt="Surgassists" class="mx-auto max-w-full h-auto" />
    </div>
       </div>
 

        <!-- Success Icon -->
        <div class="flex justify-center mb-6 pt-6">
          <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </div>

        <h1 class="text-3xl font-bold text-gray-900 text-center">
            Thank You!
        </h1>

        <p class="text-lg text-gray-600 text-center mt-4">
            Your documents have been signed successfully.
        </p>

        <p class="text-lg text-gray-600 text-center mt-4">
            Your consent is now complete, thank you for using Surgassists, 
        </p>

        
        <!-- Process Complete Card -->

        <div class="flex justify-center mt-8">
          <div class="p-6 rounded-lg shadow-md max-w-md w-full" style="background-color: #E9F8EE;">
            <h2 class="text-xl font-semibold text-black mb-4 text-center">Process Complete</h2>
            <ul class="space-y-2 text-black">
              <li class="flex items-start">
                <span class="mr-2">•</span>
                <span>Your digital signature has been recorded</span>
              </li>
              <li class="flex items-start">
                <span class="mr-2">•</span>
                <span>All consent documentation is complete</span>
              </li>
              <li class="flex items-start">
                <span class="mr-2">•</span>
                <span>You may now close this window</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="py-4"></div>
        

         <div class="flex items-center justify-center">
        <div class="px-4 sm:px-6 lg:px-8">
          <!-- Consultant contact info card -->
          <div class="max-w-7xl rounded-xl shadow-lg p-8 sm:p-10 lg:p-12" style="background-color: #EFF9FC;">
            <div class="flex-1 text-center">

              <h2 class="text-2xl sm:text-3xl font-bold text-gray-800 mb-8">
                Contact Information
              </h2>

              <!-- Contact details list -->
              <div class="space-y-4 max-w-2xl mx-auto">

                <!-- Phone -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                  </svg>
                  <span class="text-sm md:text-lg  text-gray-800">Phone: {{ getConsultantPhone() }}</span>
                </div>

                <!-- Email -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-sm md:text-lg text-gray-800">Email: {{ getConsultantEmail() }}</span>
                </div>

                <!-- Department -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <span class="text-sm md:text-lg text-gray-800">Department: {{ getConsultantDepartment() }}</span>
                </div>

                <!-- Available -->
                <div class="flex items-center gap-3">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm md:text-lg text-gray-800">Available: {{ getConsultantAvailable() }}</span>
                </div>

              </div>

            </div>
          </div>
        </div>
    </div>


        <div class="py-4"></div>


        <!-- Back to Patient Information Button -->
        <div class="flex justify-center mt-8">
          <button
            @click="goBackToPatientInfo"
            class="px-6 py-3 rounded-lg font-medium text-white transition-colors hover:opacity-90 inline-flex items-center"
            style="background-color: #65ADEE;"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to patient information
          </button>
        </div>


         <div class="py-4"></div>
          <div class="py-4"></div>
          <div class="py-4"></div>

    </div>

</template>

<script setup>
// Use patient layout (no header/navbar - hides back button)
definePageMeta({
  layout: 'patient'
})

import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// Get the route to access the ID parameter
const route = useRoute()
const patientId = route.params.id

// State management
const patient = ref(null)
const consultantProfile = ref(null)
const loading = ref(true)
const error = ref('')

console.log('Thank you page loaded for patient ID:', patientId)

// Load consent and consultant profile using Go backend APIs
const loadPatientAndConsultant = async () => {
  if (!patientId) {
    error.value = 'Patient ID is required'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''
    
    // Step 1: Get consent data from Go backend (same as patient-consent-first)
    const response = await $fetch(`/api/patient/${patientId}`)


    // Extract consent record from response (same pattern as patient-consent-first)
    let consentRecord = null
    if (response && response.document) {
      // GO backend returns nested under 'document'
      consentRecord = response.document
      console.log('Consent record loaded from response.document:', consentRecord)
    } else if (response && response.data) {
      // Maybe it's under 'data'
      consentRecord = response.data
      console.log('Consent record loaded from response.data:', consentRecord)
    } else if (response && typeof response === 'object' && response.$id) {
      // Maybe the response IS the consent record directly
      consentRecord = response
      console.log('Consent record loaded directly from response:', consentRecord)
    } else {
      console.log('Could not find consent data in response structure')
      throw new Error('Consent record not found')
    }

    patient.value = consentRecord
    console.log('Consent/Patient data loaded:', consentRecord)
    console.log('UserID from consent:', consentRecord.UserID)

    // Step 2: Get consultant profile using UserID from consent record
    if (consentRecord && consentRecord.UserID) {
      console.log('=== FETCHING CONSULTANT PROFILE ===')
      console.log('Consultant UserID:', consentRecord.UserID)

      // Call API to get consultant profile (same as patient-consent-first)
      const consultantResponse = await $fetch(`/api/admin-get-user-info/${consentRecord.UserID}`)

      console.log('Consultant API Response:', consultantResponse)

      // Store consultant profile data
      consultantProfile.value = consultantResponse
      console.log('Consultant profile loaded:', consultantProfile.value)

      if (consultantProfile.value && consultantProfile.value.profile) {
        console.log('Profile fields available:', Object.keys(consultantProfile.value.profile))
        console.log('Phone field:', consultantProfile.value.profile.Phone)
        console.log('Email field:', consultantProfile.value.profile.Email)
      }
    } else {
      console.log('No UserID found in consent record')
    }

  } catch (err) {
    console.error('Error loading consent and consultant data:', err)
    error.value = err.message || 'Failed to load information'
  } finally {
    loading.value = false
  }
}

// Helper functions to access consultant profile data
const getConsultantName = () => {
  return consultantProfile.value?.ConsultantName || 'N/A'
}

const getConsultantPhone = () => {
  console.log('getConsultantPhone - consultantProfile.value:', consultantProfile.value)
  // Data is nested under profile object (same as patient-consent-first)
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Phone) {
    console.log('getConsultantPhone - Phone field:', consultantProfile.value.profile.Phone)
    return consultantProfile.value.profile.Phone
  }
  return 'N/A'
}

const getConsultantAddress = () => {
  if (!consultantProfile.value || !consultantProfile.value.profile) return 'N/A'

  const profile = consultantProfile.value.profile
  const address1 = profile.Address1 || ''
  const address2 = profile.Address2 || ''
  const postCode = profile.PostCode || ''

  let fullAddress = address1
  if (address2) fullAddress += ', ' + address2
  if (postCode) fullAddress += ', ' + postCode

  return fullAddress || 'N/A'
}

const getConsultantEmail = () => {
  console.log('getConsultantEmail - consultantProfile.value:', consultantProfile.value)
  // Data is nested under profile object (same as patient-consent-first)
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Email) {
    console.log('getConsultantEmail - Email field:', consultantProfile.value.profile.Email)
    return consultantProfile.value.profile.Email
  }
  return 'N/A'
}

const getConsultantAvailable = () => {
  // Data is nested under profile object (same as patient-consent-first)
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Available) {
    return consultantProfile.value.profile.Available
  }
  return 'Monday - Friday, 9:00 AM - 5:00 PM' // Fallback
}

const getConsultantDepartment = () => {
  // Data is nested under profile object (same as patient-consent-first)
  if (consultantProfile.value && consultantProfile.value.profile && consultantProfile.value.profile.Department) {
    return consultantProfile.value.profile.Department
  }
  return 'Surgery Department' // Fallback
}

// Handle back to patient information button click
const goBackToPatientInfo = () => {
  // Navigate back to the patient consent second page
  navigateTo(`/patient-consent-second/${patientId}`)
}

// Load data when component mounts (no authentication required)
onMounted(async () => {
  await loadPatientAndConsultant()
})

// Set page title
useHead({
  title: 'Thank You | Surgassists'
})
</script>